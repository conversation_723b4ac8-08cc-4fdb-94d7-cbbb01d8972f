import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { ContractExtractionCallbackDto, ProposalCallbackDto } from './dto/n8n-callback.dto';
import { PrismaService } from '../../prisma.service';
import { RentalAdvanceStatus } from '../../rental-advance/enums/rental-status.enum';
import { Public } from '../../common/decorators/public.decorator';

@Controller('webhooks/n8n-callback')
@UseGuards(N8nAuthGuard)
@Public()
export class N8nCallbackController {
  private readonly logger = new Logger(N8nCallbackController.name);

  constructor(private readonly prisma: PrismaService) {}

  @Post('contract-extraction')
  @HttpCode(HttpStatus.OK)
  async handleContractExtraction(@Body() payload: ContractExtractionCallbackDto) {
    this.logger.log(`Received contract extraction callback for operation: ${payload.operationId}`);

    try {
      // Find the operation
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: payload.operationId },
        include: { contractData: true },
      });

      if (!operation) {
        this.logger.error(`Operation not found: ${payload.operationId}`);
        throw new NotFoundException('Operation not found');
      }

      if (payload.success && payload.extractedData) {
        // Success: Update with extracted data
        await this.handleExtractionSuccess(payload.operationId, payload.extractedData);
        this.logger.log(`Contract extraction completed successfully for operation: ${payload.operationId}`);
      } else {
        // Failure: Update status to failed
        await this.handleExtractionFailure(payload.operationId, payload.error || 'Unknown extraction error');
        this.logger.error(`Contract extraction failed for operation: ${payload.operationId}, error: ${payload.error}`);
      }

      return { success: true, message: 'Callback processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing contract extraction callback: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to process callback');
    }
  }

  @Post('proposal')
  @HttpCode(HttpStatus.OK)
  async handleProposal(@Body() payload: ProposalCallbackDto) {
    this.logger.log(`Received proposal callback for operation: ${payload.operationId}`);

    try {
      // Find the operation
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: payload.operationId },
      });

      if (!operation) {
        this.logger.error(`Operation not found: ${payload.operationId}`);
        throw new NotFoundException('Operation not found');
      }

      if (payload.success && payload.proposalData) {
        // Success: Update with proposal data
        await this.handleProposalSuccess(payload.operationId, payload.proposalData);
        this.logger.log(`Proposal generation completed successfully for operation: ${payload.operationId}`);
      } else {
        // Failure: Update status to failed (revert to previous state)
        await this.handleProposalFailure(payload.operationId, payload.error || 'Unknown proposal error');
        this.logger.error(`Proposal generation failed for operation: ${payload.operationId}, error: ${payload.error}`);
      }

      return { success: true, message: 'Callback processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing proposal callback: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to process callback');
    }
  }

  private async handleExtractionSuccess(operationId: string, data: any): Promise<void> {
    // Create or update contract data
    await this.prisma.rentalContractData.upsert({
      where: { rentalRequestId: operationId },
      create: {
        rentalRequestId: operationId,
        propertyAddress: data.imovel_endereco_completo,
        landlordName: data.locador_nome,
        tenantName: data.locatario_nome,
        landlordDocument: data.locador_cpf_cnpj,
        tenantDocument: data.locatario_cpf_cnpj,
        rentalGuarantee: data.garantia_modalidade,
        startDate: data.startDate ? new Date(data.startDate) : null,
        endDate: data.endDate ? new Date(data.endDate) : null,
        extractedData: data,
      },
      update: {
        propertyAddress: data.imovel_endereco_completo,
        landlordName: data.locador_nome,
        tenantName: data.locatario_nome,
        landlordDocument: data.locador_cpf_cnpj,
        tenantDocument: data.locatario_cpf_cnpj,
        rentalGuarantee: data.garantia_modalidade,
        contractTerm: data.contractTerm,
        startDate: data.startDate ? new Date(data.startDate) : null,
        endDate: data.endDate ? new Date(data.endDate) : null,
        propertyRegistry: data.propertyRegistry,
        extractedData: data,
      },
    });

    // Update operation status
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: { currentStatus: RentalAdvanceStatus.PDF_EXTRACTED },
    });

    // Add status log
    await this.addStatusLog(operationId, RentalAdvanceStatus.PDF_EXTRACTED);
  }

  private async handleExtractionFailure(operationId: string, error: string): Promise<void> {
    // Update operation status to failed
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: { currentStatus: RentalAdvanceStatus.EXTRACTION_FAILED },
    });

    // Add status log with error
    await this.addStatusLog(operationId, RentalAdvanceStatus.EXTRACTION_FAILED, { error });
  }

  private async handleProposalSuccess(operationId: string, data: any): Promise<void> {
    // Update operation with proposal data
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: {
        currentStatus: RentalAdvanceStatus.PROPOSAL_SENT,
        proposalAmount: data.proposalAmount ? parseFloat(data.proposalAmount.toString()) : null,
        monthlyRentOffer: data.monthlyRentOffer ? parseFloat(data.monthlyRentOffer.toString()) : null,
        proposedMonths: data.proposedMonths ? parseInt(data.proposedMonths.toString()) : null,
      },
    });

    // Add status log
    await this.addStatusLog(operationId, RentalAdvanceStatus.PROPOSAL_SENT);
  }

  private async handleProposalFailure(operationId: string, error: string): Promise<void> {
    // Revert to DATA_CONFIRMED status so user can retry
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: { currentStatus: RentalAdvanceStatus.DATA_CONFIRMED },
    });

    // Add status log with error
    await this.addStatusLog(operationId, RentalAdvanceStatus.DATA_CONFIRMED, { 
      error: `Proposal generation failed: ${error}` 
    });
  }

  private async addStatusLog(
    operationId: string,
    status: RentalAdvanceStatus,
    additionalData?: any,
  ): Promise<void> {
    await this.prisma.rentalRequestStatusLog.create({
      data: {
        rentalRequestId: operationId,
        status,
      },
    });
  }
}
