import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import axios from 'axios';
import * as FormData from 'form-data';
import { PrismaService } from '../../prisma.service';
import { DriveService } from '../drive/drive.service';
import { RentalAdvanceStatus } from '../../rental-advance/enums/rental-status.enum';

@Injectable()
export class N8nService {
  private readonly logger = new Logger(N8nService.name);
  private readonly baseWebhookUrl = process.env.N8N_WEBHOOK_URL;

  constructor(
    private readonly prisma: PrismaService,
    private readonly driveService: DriveService,
  ) {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N_WEBHOOK_URL não configurada');
    }
  }

  async sendWhatsappCode(phone: string, code: string): Promise<void> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando código WhatsApp para: ${phone.substring(0, 4)}****`,
      );

      const response = await axios.post(
        `${this.baseWebhookUrl}/whatsapp-code`,
        {
          phone,
          code,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (response.status !== 200) {
        throw new Error(`N8N retornou status ${response.status}`);
      }

      this.logger.log('Código WhatsApp enviado com sucesso');
    } catch (error) {
      this.logger.error('Falha ao enviar código via N8N:', error);
      throw new InternalServerErrorException(
        `Falha ao enviar código de verificação. Tente novamente. URL: ${this.baseWebhookUrl}/whatsapp-code`,
      );
    }
  }

  async sendContractForExtraction(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    try {
      // Update status to processing immediately
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.PROCESSING_EXTRACTION },
      });

      // Add status log
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.PROCESSING_EXTRACTION,
        },
      });

      this.logger.log(
        `Enviando contrato para extração: ${contractPdf.originalname} (operação: ${operationId})`,
      );

      // Construct callback URL
      const callbackUrl = `${process.env.BACKEND_URL}/api/webhooks/n8n-callback/contract-extraction`;

      const formData = new FormData();
      formData.append('file', contractPdf.buffer, {
        filename: contractPdf.originalname,
        contentType: contractPdf.mimetype,
      });
      formData.append('mimeType', contractPdf.mimetype);
      formData.append('operationId', operationId);
      formData.append('callbackUrl', callbackUrl);
      formData.append('timestamp', new Date().toISOString());

      // Fire-and-forget request to N8N
      axios.post(
        `${this.baseWebhookUrl}/extract-contract`,
        formData,
        {
          timeout: 60000, // 60 segundos para extração
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      ).catch(error => {
        this.logger.error(`Failed to send extraction request to N8N: ${error.message}`, {
          operationId,
          error: error.stack
        });
        // Update status to failed
        this.updateOperationStatus(operationId, RentalAdvanceStatus.EXTRACTION_FAILED, error.message);
      });

      this.logger.log(`Contract extraction request sent to N8N for operation: ${operationId}`);
    } catch (error) {
      this.logger.error(`Error preparing contract extraction request: ${error.message}`, error.stack);
      await this.updateOperationStatus(operationId, RentalAdvanceStatus.EXTRACTION_FAILED, error.message);
    }
  }

  private async updateOperationStatus(
    operationId: string,
    status: RentalAdvanceStatus,
    errorMessage: string
  ): Promise<void> {
    try {
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: status },
      });

      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to update operation status to ${status}: ${error.message}`);
    }
  }





  public async deleteFileFromDrive(fileUrl: string): Promise<void> {
    try {
      // Extract file ID from Google Drive URL
      const fileId = this.extractFileIdFromUrl(fileUrl);
      if (!fileId) {
        this.logger.warn(`Não foi possível extrair file ID da URL: ${fileUrl}`);
        return;
      }

      // Use DriveService to delete file directly
      await this.driveService.deleteFile(fileId);
      this.logger.log(`Arquivo ${fileId} deletado do Google Drive`);
    } catch (error) {
      this.logger.error(`Erro ao deletar arquivo do Drive:`, error);
      // Não lançar erro para não quebrar o fluxo de exclusão da operação
    }
  }

  private extractFileIdFromUrl(url: string): string | null {
    try {
      // Google Drive URLs can have different formats:
      // https://drive.google.com/file/d/FILE_ID/view
      // https://drive.google.com/open?id=FILE_ID
      // https://docs.google.com/document/d/FILE_ID/edit

      const patterns = [
        /\/file\/d\/([a-zA-Z0-9-_]+)/,
        /[?&]id=([a-zA-Z0-9-_]+)/,
        /\/d\/([a-zA-Z0-9-_]+)/
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Error extracting file ID from URL: ${error.message}`);
      return null;
    }
  }



  async requestProposal(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    try {
      // Update status to processing immediately
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: data.operationId },
        data: { currentStatus: RentalAdvanceStatus.PROCESSING_PROPOSAL },
      });

      // Add status log
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: data.operationId,
          status: RentalAdvanceStatus.PROCESSING_PROPOSAL,
        },
      });

      this.logger.log(
        `Solicitando proposta para operação: ${data.operationId}`,
      );

      // Construct callback URL
      const callbackUrl = `${process.env.BACKEND_URL}/api/webhooks/n8n-callback/proposal`;

      // Fire-and-forget request to N8N
      axios.post(
        `${this.baseWebhookUrl}/request-proposal`,
        {
          ...data,
          callbackUrl,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 30000, // 30 segundos para proposta
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      ).catch(error => {
        this.logger.error(`Failed to send proposal request to N8N: ${error.message}`, {
          operationId: data.operationId,
          error: error.stack
        });
        // Revert to DATA_CONFIRMED status so user can retry
        this.updateOperationStatus(data.operationId, RentalAdvanceStatus.DATA_CONFIRMED, error.message);
      });

      this.logger.log(`Proposal request sent to N8N for operation: ${data.operationId}`);
    } catch (error) {
      this.logger.error(`Error preparing proposal request: ${error.message}`, error.stack);
      await this.updateOperationStatus(data.operationId, RentalAdvanceStatus.DATA_CONFIRMED, error.message);
    }
  }



  async notifyStatusChange(
    operationId: string,
    newStatus: string,
    additionalData?: any,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando mudança de status: ${operationId} -> ${newStatus}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/status-notification`,
        {
          operationId,
          status: newStatus,
          timestamp: new Date().toISOString(),
          ...additionalData,
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      this.logger.log('Notificação de status enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar mudança de status via N8N:', error);
    }
  }

}
